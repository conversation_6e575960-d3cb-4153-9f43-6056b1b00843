table:
  name: user
  schema: public
configuration:
  column_config:
    app_phone_number:
      custom_name: appPhoneNumber
    created_at:
      custom_name: createdAt
    disabled_at:
      custom_name: disabledAt
    email:
      custom_name: email
    id:
      custom_name: id
    last_name:
      custom_name: lastName
    name:
      custom_name: name
    updated_at:
      custom_name: updatedAt
  custom_column_names:
    app_phone_number: appPhoneNumber
    created_at: createdAt
    email: email
    id: id
    last_name: lastName
    name: name
    updated_at: updatedAt
  custom_root_fields: {}
array_relationships:
  - name: inventoryHistories
    using:
      foreign_key_constraint_on:
        column: user_id
        table:
          name: inventory_history
          schema: public
  - name: userCompanies
    using:
      foreign_key_constraint_on:
        column: user_id
        table:
          name: user_company
          schema: public
select_permissions:
  - role: read:hasura
    permission:
      columns:
        - app_phone_number
        - created_at
        - email
        - id
        - last_name
        - name
        - updated_at
      filter: {}
    comment: ""
  - role: read_user
    permission:
      columns:
        - app_phone_number
        - created_at
        - email
        - id
        - last_name
        - name
        - updated_at
      filter:
        userCompanies:
          company_id:
            _eq: X-Hasura-Company-Id
      allow_aggregations: true
    comment: ""
