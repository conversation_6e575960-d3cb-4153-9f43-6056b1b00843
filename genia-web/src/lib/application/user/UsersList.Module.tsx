import { Column, Row } from '@pitsdepot/storybook';

import { AnchorCell } from '#appComponent/table/AnchorCell.AppComponent';
import { PaginatedListAppComponent } from '#appComponent/table/PaginatedList.AppComponent';
import { User } from '#application/user/User.Type';
import ApplicationRegistry from '#composition/Application.Registry';

const columns: Column[] = [
  {
    header: 'Nombre',
    dataKey: 'fullName',
    width: '25%',
    renderer: ({ value, url }) => <AnchorCell url={url} value={value} />,
  },
  {
    header: 'Email',
    dataKey: 'email',
    width: '20%',
  },
  {
    header: 'Teléfono',
    dataKey: 'appPhoneNumber',
    width: '20%',
  },
  {
    header: 'Role',
    dataKey: 'role',
    width: '20%',
  },
  {
    header: 'Estado',
    dataKey: 'status',
    width: '15%',
    renderer: StatusCell,
  },
];

function mapper(items: User[]): Row[] {
  return items.map((item) => ({
    id: item.id,
    fullName: { value: `${item.name} ${item.lastName}`, url: `${ApplicationRegistry.PathService.user.base()}/${item.id}` },
    email: item.email,
    appPhoneNumber: item.appPhoneNumber,
    role: item.userCompanies?.[0]?.role || 'USER',
    status: item.disabledAt,
  }));
}

export function UsersListModule() {
  return (
    <PaginatedListAppComponent
      title='Usuarios'
      columns={columns}
      useGetHook={ApplicationRegistry.UsersService.useGetUsers}
      mapper={mapper}
      showHeader={false}
    />
  );
}
