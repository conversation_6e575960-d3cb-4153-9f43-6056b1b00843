import {
    Button, IconImporter, Title,
} from '@pitsdepot/storybook';
import Theme from '@pitsdepot/storybook/theme';
import { AxiosResponse } from 'axios';
import {
    useContext,
    useEffect,
    useState
} from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import { Notification } from '#/lib/appComponent/common/Notification.Component';
import UserForm from '#appComponent/user/UserForm.Component';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import ApplicationRegistry from '#composition/Application.Registry';
import TextService from '#composition/textService/Text.Service';
import { UserRole } from '#domain/aggregates/user/User.Entity';
import { CreateUserPayload, UsersHttp } from '#infrastructure/api/http/Users.Http';
import { AuthContext } from '#infrastructure/AuthState.Context';
import { UserFormData } from '#infrastructure/implementation/application/hooks/useUserFormInputs.Hook';
import ReadModelUserOrderService from '#infrastructure/implementation/application/user/User.Service';

export interface CustomAxiosResponse extends AxiosResponse {
  statusCode?: number;
}

export const userFormInitialState: Partial<UserFormData> = {
  name: '',
  lastName: '',
  email: '',
  phoneNumber: '',
  role: UserRole.USER,
};

export function CreateUpdateUserModule() {
  const { id } = useParams();
  const [isEditing, setIsEditing] = useState<boolean>(!id);
  const [defaultInputs, setDefaultInputs] = useState<Partial<UserFormData>>(userFormInitialState);
  const [formState, setFormState] = useState<UserFormData>(userFormInitialState as UserFormData);
  const [dataLoaded, setDataLoaded] = useState<boolean>(false);

  console.log('CreateUpdateUserModule render - id:', id, 'isEditing:', isEditing, 'dataLoaded:', dataLoaded);
  const { getToken, systemUser } = useContext(AuthContext);
  const [isLoading, setIsloading] = useState(false);
  const [hasError, setHasError] = useState<boolean>(false);
  const navigate = useNavigate();
  const formHasErrors = (formError: boolean) => setHasError(formError);
  const usersPath = ApplicationRegistry.PathService.user.base();
  const text = TextService.getText();

  // Usar el hook de Hasura para obtener el usuario
  const { user: userData, loading: userLoading, error: userError } = ReadModelUserOrderService.useGetUser(id || '');

  const handleChildStateChange = (state: UserFormData) => {
    setFormState(state);
  };

  // Cargar datos del usuario usando Hasura cuando los datos estén disponibles
  useEffect(() => {
    if (id && userData && !userLoading && !dataLoaded) {
      console.log('Received user data from Hasura:', userData);

      const userFormData: UserFormData = {
        name: userData.name || '',
        lastName: userData.lastName || '',
        email: userData.email,
        phoneNumber: userData.phoneNumber,
        role: userData.role as UserRole,
      };

      setFormState(userFormData);
      setDefaultInputs(userFormData);
      console.log('Setting isEditing to false (view mode) - first time loading data');
      setIsEditing(false); // Iniciar en modo vista solo la primera vez
      setDataLoaded(true); // Marcar que los datos ya se cargaron
    }
  }, [id, userData, userLoading, dataLoaded]);

  // Manejar errores de carga
  useEffect(() => {
    if (userError) {
      console.error('Error loading user from Hasura:', userError);
      Notification({
        message: 'Error al cargar los datos del usuario',
        type: MSG_ERROR_TYPES.ERROR
      });
      navigate(usersPath);
    }
  }, [userError, navigate, usersPath]);

  const userFormInputSubmit = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    setIsloading(true);
    if (hasError) {
      Notification({ message: text.common.formFieldsRequired, type: MSG_ERROR_TYPES.ERROR });
      setIsloading(false);
      return;
    }

    const token = await getToken();

    try {
      if (id) {
        // Actualizar usuario existente
        const updatePayload = {
          name: formState.name?.trim(),
          lastName: formState.lastName?.trim(),
          phoneNumber: formState.phoneNumber?.trim(),
          role: formState.role,
        };

        await UsersHttp.updateUser(token, id, updatePayload);
        Notification({ message: 'Usuario actualizado exitosamente', type: MSG_ERROR_TYPES.SUCCESS });
      } else {
        // Crear nuevo usuario
        const createPayload: CreateUserPayload = {
          user: {
            name: formState.name?.trim(),
            lastName: formState.lastName?.trim(),
            email: formState.email?.trim(),
            phoneNumber: formState.phoneNumber?.trim(),
            role: formState.role,
          },
          companyId: systemUser.companyId,
        };

        await UsersHttp.createUser(token, createPayload);
        Notification({ message: 'Usuario creado exitosamente', type: MSG_ERROR_TYPES.SUCCESS });
      }

      navigate(usersPath);
    } catch (err: any) {
      const errorMessage = err?.response?.data?.message || (id ? 'Error al actualizar el usuario' : 'Error al crear el usuario');
      Notification({ message: errorMessage, type: MSG_ERROR_TYPES.ERROR });
    } finally {
      setIsloading(false);
    }
  };

  const handleSubmitClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    userFormInputSubmit(e);
  };

  const saveButton = isEditing
    && <Button onClick={handleSubmitClick} disabled={isLoading || hasError}>
      <div className='flex gap-2 items-center'>
        <IconImporter size={24} name='floppyDisk' />
        <span>{id ? 'Actualizar' : text.common.save}</span>
      </div>
    </Button>;

  const editButton = id && !isEditing
    && <IconImporter
      name='pencil'
      size={24}
      color={Theme.colors.dark[500]}
      onClick={() => {
        console.log('Edit button clicked, setting isEditing to true');
        setIsEditing(true);
      }}
      className='text-dark-500 hover:text-dark-700 transitin-all cursor-pointer ease-in-out border border-dark-400 rounded-full p-1'
    />;

  if ((userLoading || isLoading) && id) {
    return (
      <div className='flex flex-col w-full gap-4 p-7'>
        <div className='flex justify-center items-center h-64'>
          <div className='text-lg text-gray-600'>Cargando datos del usuario...</div>
        </div>
      </div>
    );
  }

  return (
    <div className='flex flex-col w-full gap-4 p-7'>
      <div className='flex justify-between'>
        <Title as='h2'>{id ? (isEditing ? 'Editar Usuario' : 'Detalle de Usuario') : 'Nuevo Usuario'}</Title>
        {saveButton}
      </div>
      <div className='bg-white rounded-2xl w-full p-8 max-h-min shadow-lg flex flex-col gap-6'>
        <div className='flex justify-between'>
          <div>{text.common.properties}</div>
          {editButton}
        </div>
        <UserForm
          handleChildStateChange={handleChildStateChange}
          defaultInputs={defaultInputs}
          editMode={isEditing}
          formValidation={formHasErrors}
        />
      </div>
    </div>
  );
}
